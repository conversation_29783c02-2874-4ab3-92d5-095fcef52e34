package com.ctrip.dcs.tms.transport.application.command.impl

import com.ctrip.dcs.tms.transport.api.model.DrvVehRecruitingAddSOARequestType
import com.ctrip.dcs.tms.transport.api.model.DrvVehRecruitingUpdateSOARequestType
import com.ctrip.dcs.tms.transport.api.model.OcrPassStatusModelSOA
import com.ctrip.dcs.tms.transport.application.dto.ComplianceCheckResultDTO
import com.ctrip.dcs.tms.transport.application.query.InternationalEntryService
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TmsCertificateCheckPO
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehicleRecruitingPO
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OcrComplianceDTO
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsCertificateCheckRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRecruitingRepository
import spock.lang.Specification
import spock.lang.Unroll

/**
 * DrvVehRecruitingCommandServiceImpl updateCompliance方法单元测试
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
class DrvVehRecruitingCommandServiceImplUpdateComplianceTest extends Specification {

    def testObj = new DrvVehRecruitingCommandServiceImpl()
    def internationalEntryService = Mock(InternationalEntryService)
    def vehicleRecruitingRepository = Mock(VehicleRecruitingRepository)
    def checkRepository = Mock(TmsCertificateCheckRepository)

    def setup() {
        testObj.internationalEntryService = internationalEntryService
        testObj.vehicleRecruitingRepository = vehicleRecruitingRepository
        testObj.checkRepository = checkRepository
    }

    @Unroll
    def "updateCompliance for add request - compliance check scenarios"() {
        given: "设定相关方法入参"
        def vehicleId = 12345L
        def requestType = new DrvVehRecruitingAddSOARequestType()
        requestType.setCityId(379L)
        requestType.setVehicleLicense("沪A12345")
        requestType.setNewVehOcrFieldValue("test_ocr_value")
        requestType.setOcrPassStatusList(new ArrayList<OcrPassStatusModelSOA>())

        and: "准备Mock返回的对象"
        def complianceRule = new OcrComplianceDTO()
        complianceRule.setComplianceType(complianceType)

        when:
        testObj.updateCompliance(vehicleId, requestType)

        then: "验证方法调用和Mock行为"
        1 * internationalEntryService.isInComplianceRuleGary(379L) >> complianceRule
        1 * internationalEntryService.autoComplianceCheck(complianceType, "沪A12345", "test_ocr_value") >> complianceResult
        1 * vehicleRecruitingRepository.update(_ as VehicleRecruitingPO) >> { VehicleRecruitingPO po ->
            assert po.vehicleId == vehicleId
            assert po.auditStatus == expectedAuditStatus
            return 1
        }
        1 * checkRepository.insertTmsCertificateCheck(_ as TmsCertificateCheckPO) >> { TmsCertificateCheckPO po ->
            assert po.checkId == vehicleId
            assert po.checkType == TmsTransportConstant.CertificateCheckTypeEnum.VEHICLE.getCode()
            assert po.certificateType == TmsTransportConstant.CertificateTypeEnum.AUTO_VEHICLE_AUDITSTATUS.getCode()
            assert po.checkContent == expectedReason
            assert po.checkStatus == expectedCheckStatus
            assert po.thirdCheckStatus == expectedCheckStatus
            return 1
        }
        
        and: "验证OCR状态列表更新"
        requestType.getOcrPassStatusList().size() == 1
        def ocrStatus = requestType.getOcrPassStatusList().get(0)
        ocrStatus.ocrId == 0L
        ocrStatus.ocrItem == TmsTransportConstant.ApproveItemEnum.vehicle_compliance.getCode()
        ocrStatus.passStatus == expectedPassStatus

        where: "表格方式验证多种分支调用场景"
        complianceType | complianceResult                                                        | expectedAuditStatus | expectedReason      | expectedCheckStatus | expectedPassStatus
        1              | new ComplianceCheckResultDTO(auditStatus: 2, reason: "通过")            | 2                   | "通过"              | 1                   | 1
        1              | new ComplianceCheckResultDTO(auditStatus: 3, reason: "不合规原因")      | 3                   | "不合规原因"        | 2                   | 0
        1              | new ComplianceCheckResultDTO(auditStatus: 1, reason: "需要审核")        | 1                   | "需要审核"          | 0                   | 0
        2              | null                                                                    | 1                   | ""                  | 0                   | 0
    }

    def "updateCompliance for add request - no compliance rule found"() {
        given: "设定相关方法入参"
        def vehicleId = 12345L
        def requestType = new DrvVehRecruitingAddSOARequestType()
        requestType.setCityId(379L)
        requestType.setVehicleLicense("沪A12345")
        requestType.setOcrPassStatusList(new ArrayList<OcrPassStatusModelSOA>())

        and: "Mock相关接口返回"
        internationalEntryService.isInComplianceRuleGary(379L) >> null

        when:
        testObj.updateCompliance(vehicleId, requestType)

        then: "验证方法调用次数"
        1 * internationalEntryService.isInComplianceRuleGary(379L)
        0 * internationalEntryService.autoComplianceCheck(_, _, _)
        0 * vehicleRecruitingRepository.update(_)
        0 * checkRepository.insertTmsCertificateCheck(_)
        
        and: "验证OCR状态列表未更新"
        requestType.getOcrPassStatusList().size() == 0
    }

    def "updateCompliance for add request - exception handling"() {
        given: "设定相关方法入参"
        def vehicleId = 12345L
        def requestType = new DrvVehRecruitingAddSOARequestType()
        requestType.setCityId(379L)
        requestType.setVehicleLicense("沪A12345")
        requestType.setOcrPassStatusList(new ArrayList<OcrPassStatusModelSOA>())

        and: "Mock相关接口抛出异常"
        internationalEntryService.isInComplianceRuleGary(379L) >> { throw new RuntimeException("Test exception") }

        when:
        testObj.updateCompliance(vehicleId, requestType)

        then: "验证异常被捕获，不会向上抛出"
        noExceptionThrown()
        
        and: "验证方法调用次数"
        1 * internationalEntryService.isInComplianceRuleGary(379L)
        0 * internationalEntryService.autoComplianceCheck(_, _, _)
        0 * vehicleRecruitingRepository.update(_)
        0 * checkRepository.insertTmsCertificateCheck(_)
    }

    @Unroll
    def "updateCompliance for update request - compliance check scenarios"() {
        given: "设定相关方法入参"
        def vehicleId = 12345L
        def updateVehPO = new DrvVehRecruitingUpdateSOARequestType()
        updateVehPO.setCityId(379L)
        updateVehPO.setVehicleLicense("沪A12345")
        updateVehPO.setNewVehOcrFieldValue("test_ocr_value")
        updateVehPO.setOcrPassStatusList(new ArrayList<OcrPassStatusModelSOA>())

        and: "准备Mock返回的对象"
        def complianceRule = new OcrComplianceDTO()
        complianceRule.setComplianceType(complianceType)

        when:
        testObj.updateCompliance(vehicleId, updateVehPO)

        then: "验证方法调用和Mock行为"
        1 * internationalEntryService.isInComplianceRuleGary(379L) >> complianceRule
        1 * internationalEntryService.autoComplianceCheck(complianceType, "沪A12345", "test_ocr_value") >> complianceResult
        1 * vehicleRecruitingRepository.update(_ as VehicleRecruitingPO) >> { VehicleRecruitingPO po ->
            assert po.vehicleId == vehicleId
            assert po.auditStatus == expectedAuditStatus
            return 1
        }
        1 * checkRepository.insertTmsCertificateCheck(_ as TmsCertificateCheckPO) >> { TmsCertificateCheckPO po ->
            assert po.checkId == vehicleId
            assert po.checkType == TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE.getCode()
            assert po.certificateType == TmsTransportConstant.CertificateTypeEnum.AUTO_VEHICLE_AUDITSTATUS.getCode()
            assert po.checkContent == expectedReason
            assert po.checkStatus == expectedCheckStatus
            assert po.thirdCheckStatus == expectedCheckStatus
            return 1
        }
        
        and: "验证OCR状态列表更新"
        updateVehPO.getOcrPassStatusList().size() == 1
        def ocrStatus = updateVehPO.getOcrPassStatusList().get(0)
        ocrStatus.ocrId == 0L
        ocrStatus.ocrItem == TmsTransportConstant.ApproveItemEnum.vehicle_compliance.getCode()
        ocrStatus.passStatus == expectedPassStatus

        where: "表格方式验证多种分支调用场景"
        complianceType | complianceResult                                                        | expectedAuditStatus | expectedReason      | expectedCheckStatus | expectedPassStatus
        1              | new ComplianceCheckResultDTO(auditStatus: 2, reason: "通过")            | 2                   | "通过"              | 1                   | 1
        1              | new ComplianceCheckResultDTO(auditStatus: 3, reason: "不合规原因")      | 3                   | "不合规原因"        | 2                   | 0
        1              | new ComplianceCheckResultDTO(auditStatus: 1, reason: "需要审核")        | 1                   | "需要审核"          | 0                   | 0
        2              | null                                                                    | 1                   | ""                  | 0                   | 0
    }

    def "updateCompliance for update request - no compliance rule found"() {
        given: "设定相关方法入参"
        def vehicleId = 12345L
        def updateVehPO = new DrvVehRecruitingUpdateSOARequestType()
        updateVehPO.setCityId(379L)
        updateVehPO.setVehicleLicense("沪A12345")
        updateVehPO.setOcrPassStatusList(new ArrayList<OcrPassStatusModelSOA>())

        and: "Mock相关接口返回"
        internationalEntryService.isInComplianceRuleGary(379L) >> null

        when:
        testObj.updateCompliance(vehicleId, updateVehPO)

        then: "验证方法调用次数"
        1 * internationalEntryService.isInComplianceRuleGary(379L)
        0 * internationalEntryService.autoComplianceCheck(_, _, _)
        0 * vehicleRecruitingRepository.update(_)
        0 * checkRepository.insertTmsCertificateCheck(_)
        
        and: "验证OCR状态列表未更新"
        updateVehPO.getOcrPassStatusList().size() == 0
    }

    def "updateCompliance for update request - exception handling"() {
        given: "设定相关方法入参"
        def vehicleId = 12345L
        def updateVehPO = new DrvVehRecruitingUpdateSOARequestType()
        updateVehPO.setCityId(379L)
        updateVehPO.setVehicleLicense("沪A12345")
        updateVehPO.setOcrPassStatusList(new ArrayList<OcrPassStatusModelSOA>())

        and: "Mock相关接口抛出异常"
        internationalEntryService.isInComplianceRuleGary(379L) >> { throw new RuntimeException("Test exception") }

        when:
        testObj.updateCompliance(vehicleId, updateVehPO)

        then: "验证异常被捕获，不会向上抛出"
        noExceptionThrown()

        and: "验证方法调用次数"
        1 * internationalEntryService.isInComplianceRuleGary(379L)
        0 * internationalEntryService.autoComplianceCheck(_, _, _)
        0 * vehicleRecruitingRepository.update(_)
        0 * checkRepository.insertTmsCertificateCheck(_)
    }

    def "updateCompliance for add request - null vehicle license"() {
        given: "设定相关方法入参"
        def vehicleId = 12345L
        def requestType = new DrvVehRecruitingAddSOARequestType()
        requestType.setCityId(379L)
        requestType.setVehicleLicense(null)
        requestType.setNewVehOcrFieldValue("test_ocr_value")
        requestType.setOcrPassStatusList(new ArrayList<OcrPassStatusModelSOA>())

        and: "准备Mock返回的对象"
        def complianceRule = new OcrComplianceDTO()
        complianceRule.setComplianceType(1)
        
        def complianceResult = new ComplianceCheckResultDTO()
        complianceResult.setAuditStatus(1)
        complianceResult.setReason("车牌号为空")

        when:
        testObj.updateCompliance(vehicleId, requestType)

        then: "验证方法调用和Mock行为"
        1 * internationalEntryService.isInComplianceRuleGary(379L) >> complianceRule
        1 * internationalEntryService.autoComplianceCheck(1, null, "test_ocr_value") >> complianceResult
        1 * vehicleRecruitingRepository.update(_ as VehicleRecruitingPO) >> { VehicleRecruitingPO po ->
            assert po.vehicleId == vehicleId
            assert po.auditStatus == 1
            return 1
        }
        1 * checkRepository.insertTmsCertificateCheck(_ as TmsCertificateCheckPO) >> { TmsCertificateCheckPO po ->
            assert po.checkId == vehicleId
            assert po.checkContent == "车牌号为空"
            return 1
        }
        
        and: "验证没有异常抛出"
        noExceptionThrown()
        
        and: "验证OCR状态列表更新"
        requestType.getOcrPassStatusList().size() == 1
    }

    def "updateCompliance for update request - null OCR field value"() {
        given: "设定相关方法入参"
        def vehicleId = 12345L
        def updateVehPO = new DrvVehRecruitingUpdateSOARequestType()
        updateVehPO.setCityId(379L)
        updateVehPO.setVehicleLicense("沪A12345")
        updateVehPO.setNewVehOcrFieldValue(null)
        updateVehPO.setOcrPassStatusList(new ArrayList<OcrPassStatusModelSOA>())

        and: "准备Mock返回的对象"
        def complianceRule = new OcrComplianceDTO()
        complianceRule.setComplianceType(1)
        
        def complianceResult = new ComplianceCheckResultDTO()
        complianceResult.setAuditStatus(1)
        complianceResult.setReason("OCR字段值为空")

        when:
        testObj.updateCompliance(vehicleId, updateVehPO)

        then: "验证方法调用和Mock行为"
        1 * internationalEntryService.isInComplianceRuleGary(379L) >> complianceRule
        1 * internationalEntryService.autoComplianceCheck(1, "沪A12345", null) >> complianceResult
        1 * vehicleRecruitingRepository.update(_ as VehicleRecruitingPO) >> { VehicleRecruitingPO po ->
            assert po.vehicleId == vehicleId
            assert po.auditStatus == 1
            return 1
        }
        1 * checkRepository.insertTmsCertificateCheck(_ as TmsCertificateCheckPO) >> { TmsCertificateCheckPO po ->
            assert po.checkId == vehicleId
            assert po.checkContent == "OCR字段值为空"
            return 1
        }
        
        and: "验证没有异常抛出"
        noExceptionThrown()
        
        and: "验证OCR状态列表更新"
        updateVehPO.getOcrPassStatusList().size() == 1
    }

    def "updateCompliance for add request - repository update failure"() {
        given: "设定相关方法入参"
        def vehicleId = 12345L
        def requestType = new DrvVehRecruitingAddSOARequestType()
        requestType.setCityId(379L)
        requestType.setVehicleLicense("沪A12345")
        requestType.setNewVehOcrFieldValue("test_ocr_value")
        requestType.setOcrPassStatusList(new ArrayList<OcrPassStatusModelSOA>())

        and: "准备Mock返回的对象"
        def complianceRule = new OcrComplianceDTO()
        complianceRule.setComplianceType(1)
        
        def complianceResult = new ComplianceCheckResultDTO()
        complianceResult.setAuditStatus(1)
        complianceResult.setReason("检查通过")

        when:
        testObj.updateCompliance(vehicleId, requestType)

        then: "验证方法调用和Mock行为"
        1 * internationalEntryService.isInComplianceRuleGary(379L) >> complianceRule
        1 * internationalEntryService.autoComplianceCheck(1, "沪A12345", "test_ocr_value") >> complianceResult
        1 * vehicleRecruitingRepository.update(_ as VehicleRecruitingPO) >> 0  // 更新失败
        1 * checkRepository.insertTmsCertificateCheck(_ as TmsCertificateCheckPO) >> { TmsCertificateCheckPO po ->
            assert po.checkId == vehicleId
            assert po.checkContent == "检查通过"
            return 1
        }
        
        and: "验证没有异常抛出"
        noExceptionThrown()
        
        and: "验证OCR状态列表更新"
        requestType.getOcrPassStatusList().size() == 1
    }
}
