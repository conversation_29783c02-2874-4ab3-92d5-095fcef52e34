package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehicleRecruitingPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.VehicleAuditStatusEnum;
import com.ctrip.igt.framework.common.clogging.Logger;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 测试招募入驻合规字段准确率埋点功能
 */
@RunWith(MockitoJUnitRunner.class)
public class RecruitingCommandServiceImplComplianceAccuracyTest {

    @InjectMocks
    private RecruitingCommandServiceImpl recruitingCommandService;

    @Mock
    private Logger log;

    @Test
    public void testRecordRecruitingComplianceAccuracyMetrics_Accurate() throws Exception {
        // 测试准确的情况：原始状态和最终状态相同
        VehicleRecruitingPO vehicleRecruitingPO1 = new VehicleRecruitingPO();
        vehicleRecruitingPO1.setAuditStatus(VehicleAuditStatusEnum.COMPLIANCE.getCode()); // 合规
        
        VehicleAuditStatusEnum enumBySinaleApproveStatus = VehicleAuditStatusEnum.COMPLIANCE; // 合规
        Long sourceId = 12345L;

        // 使用反射调用私有方法
        Method method = RecruitingCommandServiceImpl.class.getDeclaredMethod(
                "recordRecruitingComplianceAccuracyMetrics", 
                VehicleRecruitingPO.class, VehicleAuditStatusEnum.class, Long.class);
        method.setAccessible(true);
        method.invoke(recruitingCommandService, vehicleRecruitingPO1, enumBySinaleApproveStatus, sourceId);

        // 验证日志记录
        verify(log, times(2)).info(anyString(), any(Object[].class));
    }

    @Test
    public void testRecordRecruitingComplianceAccuracyMetrics_Inaccurate() throws Exception {
        // 测试不准确的情况：原始状态和最终状态不同
        VehicleRecruitingPO vehicleRecruitingPO1 = new VehicleRecruitingPO();
        vehicleRecruitingPO1.setAuditStatus(VehicleAuditStatusEnum.UNDISPOSED.getCode()); // 待人工审核
        
        VehicleAuditStatusEnum enumBySinaleApproveStatus = VehicleAuditStatusEnum.DISQUALIFICATION; // 不合规
        Long sourceId = 67890L;

        // 使用反射调用私有方法
        Method method = RecruitingCommandServiceImpl.class.getDeclaredMethod(
                "recordRecruitingComplianceAccuracyMetrics", 
                VehicleRecruitingPO.class, VehicleAuditStatusEnum.class, Long.class);
        method.setAccessible(true);
        method.invoke(recruitingCommandService, vehicleRecruitingPO1, enumBySinaleApproveStatus, sourceId);

        // 验证日志记录
        verify(log, times(2)).info(anyString(), any(Object[].class));
    }

    @Test
    public void testGetRecruitingComplianceStatusTag() throws Exception {
        // 测试合规状态标签转换
        Method method = RecruitingCommandServiceImpl.class.getDeclaredMethod(
                "getRecruitingComplianceStatusTag", Integer.class);
        method.setAccessible(true);

        // 测试各种状态值的转换
        assert "undisposed".equals(method.invoke(recruitingCommandService, VehicleAuditStatusEnum.UNDISPOSED.getCode()));
        assert "compliance".equals(method.invoke(recruitingCommandService, VehicleAuditStatusEnum.COMPLIANCE.getCode()));
        assert "disqualification".equals(method.invoke(recruitingCommandService, VehicleAuditStatusEnum.DISQUALIFICATION.getCode()));
        assert "unknown".equals(method.invoke(recruitingCommandService, (Integer) null));
        assert "other_99".equals(method.invoke(recruitingCommandService, 99));
    }

    @Test
    public void testRecordRecruitingComplianceAccuracyMetrics_NullValues() throws Exception {
        // 测试空值情况
        VehicleRecruitingPO vehicleRecruitingPO1 = null;
        VehicleAuditStatusEnum enumBySinaleApproveStatus = null;
        Long sourceId = 11111L;

        // 使用反射调用私有方法
        Method method = RecruitingCommandServiceImpl.class.getDeclaredMethod(
                "recordRecruitingComplianceAccuracyMetrics", 
                VehicleRecruitingPO.class, VehicleAuditStatusEnum.class, Long.class);
        method.setAccessible(true);
        method.invoke(recruitingCommandService, vehicleRecruitingPO1, enumBySinaleApproveStatus, sourceId);

        // 验证日志记录
        verify(log, times(2)).info(anyString(), any(Object[].class));
    }

    @Test
    public void testRecordRecruitingComplianceAccuracyMetrics_PartialNullValues() throws Exception {
        // 测试部分空值情况
        VehicleRecruitingPO vehicleRecruitingPO1 = new VehicleRecruitingPO();
        vehicleRecruitingPO1.setAuditStatus(null); // 原始状态为空
        
        VehicleAuditStatusEnum enumBySinaleApproveStatus = VehicleAuditStatusEnum.COMPLIANCE; // 最终状态为合规
        Long sourceId = 22222L;

        // 使用反射调用私有方法
        Method method = RecruitingCommandServiceImpl.class.getDeclaredMethod(
                "recordRecruitingComplianceAccuracyMetrics", 
                VehicleRecruitingPO.class, VehicleAuditStatusEnum.class, Long.class);
        method.setAccessible(true);
        method.invoke(recruitingCommandService, vehicleRecruitingPO1, enumBySinaleApproveStatus, sourceId);

        // 验证日志记录
        verify(log, times(2)).info(anyString(), any(Object[].class));
    }

    @Test
    public void testAllStatusTransitions() throws Exception {
        // 测试所有可能的状态转换组合
        VehicleAuditStatusEnum[] statuses = VehicleAuditStatusEnum.values();
        
        for (VehicleAuditStatusEnum originalStatus : statuses) {
            for (VehicleAuditStatusEnum finalStatus : statuses) {
                VehicleRecruitingPO vehicleRecruitingPO1 = new VehicleRecruitingPO();
                vehicleRecruitingPO1.setAuditStatus(originalStatus.getCode());
                
                Long sourceId = 33333L;

                // 使用反射调用私有方法
                Method method = RecruitingCommandServiceImpl.class.getDeclaredMethod(
                        "recordRecruitingComplianceAccuracyMetrics", 
                        VehicleRecruitingPO.class, VehicleAuditStatusEnum.class, Long.class);
                method.setAccessible(true);
                method.invoke(recruitingCommandService, vehicleRecruitingPO1, finalStatus, sourceId);
            }
        }

        // 验证日志记录次数（3x3=9种组合，每种组合记录2次日志）
        verify(log, times(18)).info(anyString(), any(Object[].class));
    }
}
